import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';

/// مدير الأداء الحرج - حلول فورية للأجهزة المتوسطة والضعيفة
class CriticalPerformanceManager {
  static final CriticalPerformanceManager _instance = CriticalPerformanceManager._internal();
  factory CriticalPerformanceManager() => _instance;
  CriticalPerformanceManager._internal();

  bool _isInitialized = false;
  bool _isLowEndDevice = false;

  /// تهيئة فورية لتحسينات الأداء الحرجة
  Future<void> initializeCriticalOptimizations() async {
    if (_isInitialized) return;

    try {
      // تحديد نوع الجهاز
      _detectDeviceCapability();
      
      // تطبيق تحسينات فورية
      await _applyCriticalOptimizations();
      
      // تحسين إعدادات Flutter
      _optimizeFlutterSettings();
      
      _isInitialized = true;
      debugPrint('✅ تم تطبيق التحسينات الحرجة للأداء');
    } catch (e) {
      debugPrint('❌ خطأ في تطبيق التحسينات: $e');
    }
  }

  /// تحديد قدرات الجهاز
  void _detectDeviceCapability() {
    // افتراض أن الجهاز ضعيف حتى يثبت العكس (للأمان)
    _isLowEndDevice = true;
    debugPrint('🔍 تم تحديد الجهاز كجهاز متوسط/ضعيف - تطبيق تحسينات قوية');
  }

  /// تطبيق تحسينات حرجة فورية
  Future<void> _applyCriticalOptimizations() async {
    // تحسين التخزين المؤقت للصور
    PaintingBinding.instance.imageCache.maximumSize = 30; // تقليل جذري
    PaintingBinding.instance.imageCache.maximumSizeBytes = 15 * 1024 * 1024; // 15MB فقط

    // تنظيف فوري للذاكرة
    PaintingBinding.instance.imageCache.clear();
    
    // تحسين إعدادات الرسم
    debugPrint('🎨 تم تحسين إعدادات الرسم والتخزين المؤقت');
  }

  /// تحسين إعدادات Flutter
  void _optimizeFlutterSettings() {
    // تحسين جدولة الإطارات
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // تحسين أولوية الرسم
      SchedulerBinding.instance.ensureVisualUpdate();
    });

    debugPrint('⚙️ تم تحسين إعدادات Flutter');
  }

  /// إنشاء قائمة محسنة للأجهزة الضعيفة
  Widget buildUltraOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    // تقليل عدد العناصر للأجهزة الضعيفة
    final effectiveItemCount = _isLowEndDevice 
        ? (itemCount > 15 ? 15 : itemCount) 
        : itemCount;

    return ListView.builder(
      controller: controller,
      itemCount: effectiveItemCount,
      itemBuilder: (context, index) {
        // RepaintBoundary إجباري لكل عنصر
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      physics: physics ?? const ClampingScrollPhysics(),
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      shrinkWrap: shrinkWrap,
      cacheExtent: 100, // تقليل جذري للتخزين المؤقت
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false, // نحن نضيفها يدوياً
      addSemanticIndexes: false,
    );
  }

  /// إنشاء شبكة محسنة للأجهزة الضعيفة
  Widget buildUltraOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    ScrollController? controller,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    double childAspectRatio = 1.0,
    bool shrinkWrap = false,
  }) {
    final effectiveItemCount = _isLowEndDevice 
        ? (itemCount > 12 ? 12 : itemCount) 
        : itemCount;

    return GridView.builder(
      controller: controller,
      itemCount: effectiveItemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      physics: physics ?? const ClampingScrollPhysics(),
      padding: padding ?? const EdgeInsets.all(4),
      shrinkWrap: shrinkWrap,
      cacheExtent: 80,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: false,
      addSemanticIndexes: false,
    );
  }

  /// انتقال فوري محسن
  PageRouteBuilder<T> buildUltraFastTransition<T>({
    required Widget page,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 150), // فائق السرعة
      reverseTransitionDuration: const Duration(milliseconds: 100),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // انتقال بسيط جداً - فقط تلاشي
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  /// صورة محسنة للأجهزة الضعيفة
  Widget buildUltraOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    bool isAsset = true,
  }) {
    // تقليل جذري في الأبعاد للأجهزة الضعيفة
    final scale = _isLowEndDevice ? 0.6 : 0.8;
    final optimizedWidth = width != null ? (width * scale) : null;
    final optimizedHeight = height != null ? (height * scale) : null;

    if (isAsset) {
      return Image.asset(
        imagePath,
        width: optimizedWidth,
        height: optimizedHeight,
        fit: fit,
        cacheWidth: optimizedWidth?.round(),
        cacheHeight: optimizedHeight?.round(),
        filterQuality: FilterQuality.low, // أقل جودة للأداء الأفضل
        errorBuilder: (context, error, stackTrace) => Container(
          width: optimizedWidth,
          height: optimizedHeight,
          color: Colors.grey[300],
          child: const Icon(Icons.error, size: 16),
        ),
      );
    } else {
      return Image.network(
        imagePath,
        width: optimizedWidth,
        height: optimizedHeight,
        fit: fit,
        cacheWidth: optimizedWidth?.round(),
        cacheHeight: optimizedHeight?.round(),
        filterQuality: FilterQuality.low,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: optimizedWidth,
            height: optimizedHeight,
            color: Colors.grey[100],
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => Container(
          width: optimizedWidth,
          height: optimizedHeight,
          color: Colors.grey[300],
          child: const Icon(Icons.error, size: 16),
        ),
      );
    }
  }

  /// بطاقة محسنة للأجهزة الضعيفة
  Widget buildUltraOptimizedCard({
    required Widget child,
    required BuildContext context,
    EdgeInsets? margin,
    EdgeInsets? padding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? const EdgeInsets.all(4),
      padding: padding ?? const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        // ظل مبسط جداً أو بدون ظل للأجهزة الضعيفة
        boxShadow: _isLowEndDevice ? null : [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  /// تنظيف فوري للذاكرة
  Future<void> performEmergencyCleanup() async {
    try {
      // تنظيف شامل للتخزين المؤقت
      PaintingBinding.instance.imageCache.clear();
      
      // تقليل حجم التخزين المؤقت
      PaintingBinding.instance.imageCache.maximumSize = 20;
      PaintingBinding.instance.imageCache.maximumSizeBytes = 10 * 1024 * 1024;
      
      // إجبار تحديث الواجهة
      SchedulerBinding.instance.ensureVisualUpdate();
      
      debugPrint('🧹 تم تنظيف الذاكرة بشكل طارئ');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الذاكرة: $e');
    }
  }

  /// الحصول على إعدادات محسنة للأجهزة الضعيفة
  Map<String, dynamic> getUltraOptimizedSettings() {
    return {
      'isLowEndDevice': _isLowEndDevice,
      'maxCacheSize': _isLowEndDevice ? 20 : 50,
      'maxCacheBytes': _isLowEndDevice ? 10 * 1024 * 1024 : 25 * 1024 * 1024,
      'cacheExtent': _isLowEndDevice ? 80 : 150,
      'maxVisibleItems': _isLowEndDevice ? 12 : 20,
      'transitionDuration': _isLowEndDevice ? 150 : 200,
      'imageQuality': _isLowEndDevice ? 'low' : 'medium',
      'enableShadows': !_isLowEndDevice,
      'enableAnimations': !_isLowEndDevice,
    };
  }

  /// طباعة إحصائيات الأداء
  void printPerformanceStats() {
    final settings = getUltraOptimizedSettings();
    debugPrint('📊 إحصائيات الأداء المحسن:');
    debugPrint('  - نوع الجهاز: ${_isLowEndDevice ? "ضعيف/متوسط" : "قوي"}');
    debugPrint('  - حجم التخزين المؤقت: ${settings['maxCacheSize']}');
    debugPrint('  - العناصر المرئية: ${settings['maxVisibleItems']}');
    debugPrint('  - مدة الانتقال: ${settings['transitionDuration']}ms');
    debugPrint('  - جودة الصور: ${settings['imageQuality']}');
  }

  /// إعادة تعيين جميع التحسينات
  void reset() {
    _isInitialized = false;
    _isLowEndDevice = false;
    debugPrint('🔄 تم إعادة تعيين مدير الأداء الحرج');
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLowEndDevice => _isLowEndDevice;
}
